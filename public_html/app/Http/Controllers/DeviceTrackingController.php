<?php

namespace App\Http\Controllers;

use App\Models\SelfUid;
use App\Models\SelfUidTracking;
use Illuminate\Http\Request;
use Carbon\Carbon;

class DeviceTrackingController extends Controller
{
    /**
     * Hiển thị trang Device Tracking với 2 bảng: inactive devices và all devices
     */
    public function index(Request $request)
    {
        $minutes = $request->get('minutes', 30);
        $sortBy = $request->get('sort_by', 'total_diamond');
        $sortDirection = $request->get('sort_direction', 'desc');
        $searchDevice = $request->get('search_device', '');
        $inactivePage = $request->get('inactive_page', 1);
        $allPage = $request->get('all_page', 1);
        
        // Validate parameters
        if (!is_numeric($minutes) || $minutes < 1 || $minutes > 1440) {
            $minutes = 30;
        }

        $allowedSortFields = ['device_name', 'total_devices', 'total_diamond', 'total_bean', 'last_activity'];
        if (!in_array($sortBy, $allowedSortFields)) {
            $sortBy = 'total_diamond';
        }

        if (!in_array($sortDirection, ['asc', 'desc'])) {
            $sortDirection = 'desc';
        }

        // Get inactive devices
        $inactiveDevices = $this->getInactiveDevices($minutes, $sortBy, $sortDirection, $searchDevice);
        $inactiveDevicesPaginated = $this->paginateCollection($inactiveDevices, 20, $inactivePage, 'inactive_page');

        // Get all devices
        $allDevices = $this->getAllDevices($sortBy, $sortDirection, $searchDevice);
        $allDevicesPaginated = $this->paginateCollection($allDevices, 20, $allPage, 'all_page');

        // Statistics
        $stats = $this->getDeviceStats($minutes, $searchDevice);

        return view('admin.device-tracking.index', compact(
            'inactiveDevicesPaginated',
            'allDevicesPaginated',
            'stats',
            'minutes',
            'sortBy',
            'sortDirection',
            'searchDevice'
        ));
    }

    /**
     * Lấy danh sách device không hoạt động
     */
    private function getInactiveDevices($minutes, $sortBy, $sortDirection, $searchDevice = '')
    {
        $cutoffTime = Carbon::now()->subMinutes($minutes);
        
        // Lấy danh sách device_name đã hoạt động trong khoảng thời gian gần nhất
        $activeDeviceNames = SelfUid::where('requested_at', '>=', $cutoffTime)
                                  ->whereNotNull('device_name')
                                  ->where('device_name', '!=', '')
                                  ->when($searchDevice, function($query) use ($searchDevice) {
                                      return $query->where('device_name', 'LIKE', '%' . $searchDevice . '%');
                                  })
                                  ->distinct()
                                  ->pluck('device_name');
        
        // Lấy các device không hoạt động
        $rangeTime = Carbon::now()->subHours(48);
        $inactiveDevicesQuery = SelfUid::where('requested_at', '<', $cutoffTime)
                                ->where('requested_at', '>=', $rangeTime)
                                ->whereNotNull('device_name')
                                ->where('device_name', '!=', '')
                                ->whereNotIn('device_name', $activeDeviceNames);

        if ($searchDevice) {
            $inactiveDevicesQuery->where('device_name', 'LIKE', '%' . $searchDevice . '%');
        }

        $inactiveDevices = $inactiveDevicesQuery->select('device_name', 'self_uid', 'requested_at', 'diamond_balance', 'bean_balance', 'user_token')
                                ->with('user:id,name,user_token')
                                ->orderBy('requested_at', 'desc')
                                ->get();
        
        $grouped = $inactiveDevices->groupBy('device_name')
                              ->map(function ($devices, $deviceName) {
                                  $totalDevices = $devices->count();
                                  $totalDiamond = $devices->sum('diamond_balance');
                                  $totalBean = $devices->sum('bean_balance');
                                  $lastActivity = $devices->max('requested_at');
                                  
                                  return [
                                      'device_name' => $deviceName,
                                      'total_devices' => $totalDevices,
                                      'total_diamond' => $totalDiamond,
                                      'total_bean' => $totalBean,
                                      'last_activity' => $lastActivity,
                                      'devices' => $devices->take(5)
                                  ];
                              });

        // Apply sorting
        if ($sortDirection === 'asc') {
            return $grouped->sortBy($sortBy);
        } else {
            return $grouped->sortByDesc($sortBy);
        }
    }

    /**
     * Lấy danh sách tất cả device
     */
    private function getAllDevices($sortBy, $sortDirection, $searchDevice = '')
    {
        $query = SelfUid::whereNotNull('device_name')
                        ->where('device_name', '!=', '');
        // 1. Xác định thời gian giới hạn là xx giờ trước
        $cutoffTime = Carbon::now()->subHours(24);

        // 2. Thêm điều kiện lọc theo thời gian vào câu truy vấn
        $query->where('requested_at', '>=', $cutoffTime);
        
        // 3. Thêm điều kiện tìm kiếm device_name
        if ($searchDevice) {
            $query->where('device_name', 'LIKE', '%' . $searchDevice . '%');
        }

        $allDevices = $query->select('device_name', 'self_uid', 'requested_at', 'updated_at', 'diamond_balance', 'bean_balance', 'user_token')
                           ->with('user:id,name,user_token')
                           ->orderBy('requested_at', 'desc')
                           ->get();
        
        $grouped = $allDevices->groupBy('device_name')
                             ->map(function ($devices, $deviceName) {
                                 $totalDevices = $devices->count();
                                 $totalDiamond = $devices->sum('diamond_balance');
                                 $totalBean = $devices->sum('bean_balance');
                                 $lastActivity = $devices->max('requested_at');

                                 // Lấy thời điểm cập nhật mới nhất khi diamond_balance thực sự có thay đổi
                                 $selfUids = $devices->pluck('self_uid')->unique();
                                 $lastDiamondUpdate = $this->getLastDiamondChangeTime($selfUids);

                                 return [
                                     'device_name' => $deviceName,
                                     'total_devices' => $totalDevices,
                                     'total_diamond' => $totalDiamond,
                                     'total_bean' => $totalBean,
                                     'last_activity' => $lastActivity,
                                     'last_update' => $lastDiamondUpdate ?: $devices->max('updated_at'),
                                     'devices' => $devices->take(5)
                                 ];
                             });

        // Apply sorting
        if ($sortDirection === 'asc') {
            return $grouped->sortBy($sortBy);
        } else {
            return $grouped->sortByDesc($sortBy);
        }
    }

    /**
     * Tính toán thống kê device
     */
    private function getDeviceStats($minutes, $searchDevice = '')
    {
        $cutoffTime = Carbon::now()->subMinutes($minutes);
        
        $baseQuery = SelfUid::whereNotNull('device_name')->where('device_name', '!=', '');
        
        if ($searchDevice) {
            $baseQuery->where('device_name', 'LIKE', '%' . $searchDevice . '%');
        }

        // Active devices
        $activeDevicesCount = (clone $baseQuery)->where('requested_at', '>=', $cutoffTime)
                                               ->distinct('device_name')
                                               ->count();

        // All devices
        $totalDevicesCount = (clone $baseQuery)->distinct('device_name')->count();

        // Inactive devices
        $activeDeviceNames = (clone $baseQuery)->where('requested_at', '>=', $cutoffTime)
                                              ->distinct()
                                              ->pluck('device_name');
        
        $inactiveDevicesCount = (clone $baseQuery)->where('requested_at', '<', $cutoffTime)
                                                 ->whereNotIn('device_name', $activeDeviceNames)
                                                 ->distinct('device_name')
                                                 ->count();

        // Total Self UIDs
        $totalSelfUids = (clone $baseQuery)->count();
        $activeSelfUids = (clone $baseQuery)->where('requested_at', '>=', $cutoffTime)->count();
        $inactiveSelfUids = $totalSelfUids - $activeSelfUids;

        // Diamond & Bean totals
        // $totalDiamond = (clone $baseQuery)->sum('diamond_balance');
        // $totalBean = (clone $baseQuery)->sum('bean_balance');

        return [
            'minutes' => $minutes,
            'total_devices_count' => $totalDevicesCount,
            'active_devices_count' => $activeDevicesCount,
            'inactive_devices_count' => $inactiveDevicesCount,
            'total_self_uids' => $totalSelfUids,
            'active_self_uids' => $activeSelfUids,
            'inactive_self_uids' => $inactiveSelfUids,
            // 'total_diamond' => $totalDiamond,
            // 'total_bean' => $totalBean,
            'search_device' => $searchDevice
        ];
    }

    /**
     * Lấy thời điểm cuối cùng mà diamond_balance thực sự thay đổi
     */
    private function getLastDiamondChangeTime($selfUids)
    {
        if ($selfUids->isEmpty()) {
            return null;
        }

        $lastChangeTime = null;
        $cutoffTime = Carbon::now()->subHours(24);

        // Lấy tất cả tracking records trong 24h qua cho các self_uid này
        $trackings = SelfUidTracking::whereIn('self_uid', $selfUids)
            ->where('created_at', '>=', $cutoffTime)
            ->orderBy('self_uid')
            ->orderBy('created_at')
            ->get()
            ->groupBy('self_uid');

        foreach ($trackings as $selfUid => $records) {
            $previousBalance = null;

            foreach ($records as $record) {
                // Nếu đây là bản ghi đầu tiên hoặc diamond_balance có thay đổi
                if ($previousBalance === null || $record->diamond_balance != $previousBalance) {
                    if (!$lastChangeTime || $record->created_at > $lastChangeTime) {
                        $lastChangeTime = $record->created_at;
                    }
                }
                $previousBalance = $record->diamond_balance;
            }
        }

        return $lastChangeTime;
    }

    /**
     * Phân trang cho collection
     */
    private function paginateCollection($collection, $perPage, $currentPage, $pageName)
    {
        $currentPage = max(1, (int) $currentPage);
        $total = $collection->count();
        $offset = ($currentPage - 1) * $perPage;
        $items = $collection->slice($offset, $perPage)->values();

        return new \Illuminate\Pagination\LengthAwarePaginator(
            $items,
            $total,
            $perPage,
            $currentPage,
            [
                'path' => request()->url(),
                'pageName' => $pageName,
                'query' => request()->except($pageName)
            ]
        );
    }
}
